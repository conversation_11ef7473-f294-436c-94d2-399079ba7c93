# GitHub Copilot Instructions for TEST: Agent Configuration Validation

CORE RULES: Follow ALL rules from docs/tech-specs/process/agent-rules/core.mdx

MANDATORY ACTIONS:
- Create work-log/milestone-test/requirement-checklist.md before starting
- Update work logs in real-time during implementation
- Validate ALL success criteria immediately after implementation
- Use package managers for dependencies, never edit package files manually
- Run acceptance tests immediately after completion

QUALITY GATES:
- Implement ALL specified success criteria (no skipping)
- Achieve minimum test coverage thresholds: Unit (80%), Integration (70%)
- Pass all linting and type checking
- Include comprehensive error handling

GIT WORKFLOW REQUIREMENTS:
- Create milestone branch: milestone/m{X}-{description}
- Create task branches: m{X}/task-{##}-{description}
- Use conventional commit messages: type(scope): description
- Follow branching strategy from git-workflow.mdx
- Squash commits when merging to milestone branch

DOCUMENTATION REQUIREMENTS:
- Update documentation in real-time (max 15min lag)
- Validate all documentation using spec-lint.mjs
- Replace ALL placeholders in templates
- Maintain cross-references and links
- Follow documentation standards from documentation.mdx

ARCHITECTURAL DECISION REQUIREMENTS:
- Create ADR for significant architectural decisions
- Use ADR template from templates/adr-template.mdx
- Follow naming convention: adr-XXX-short-title.mdx
- Update ADR index in adrs/log.mdx
- Include proper rationale and consequences

ERROR RECOVERY REQUIREMENTS:
- Stop implementation when critical errors detected
- Document all errors and resolution attempts
- Seek human intervention for unresolvable issues
- Maintain rollback capability at all times
- Follow error recovery procedures from error-recovery.mdx

PROCESS REFERENCES:
- Detailed process: docs/tech-specs/process/core/milestone-implementation.mdx
- Quality standards: docs/tech-specs/process/core/quality-assurance.mdx
- Error handling: docs/tech-specs/process/core/error-recovery.mdx

MILESTONE-SPECIFIC REQUIREMENTS:
- Implement capitalizeWords(input: string): string function
- Create comprehensive unit tests with 80%+ coverage
- Add JSDoc documentation and usage examples
- Follow TypeScript strict mode compliance
- Complete all process validation requirements

SUCCESS CRITERIA CHECKLIST:
1. String utility function implemented correctly
2. Comprehensive testing with 80%+ coverage
3. Complete documentation with examples
4. Pre-implementation validation completed
5. Real-time documentation maintained
6. Quality assurance requirements met
7. Setup efficiency < 5 minutes
8. Process compliance 100%
9. All quality gates passed
10. Agent confidence 8/10 or higher

VALIDATION COMMANDS:
- Run tests: npm test -- string-utils.test.ts
- Check coverage: npm run test:coverage
- Type check: npm run type-check
- Lint check: npm run lint
- Acceptance tests: bash scripts/test-acceptance.sh
