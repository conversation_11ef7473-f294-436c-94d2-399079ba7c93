---
title: Milestone M1 — Static Graph Builder
description: Implements static code analysis and JSON-LD graph extraction for Python/JS using Tree-sitter.
created: 2024-05-25
version: 0.2.0
status: Draft
tags: [milestone]
authors: [nitishMehrotra]
---



<Callout emoji="🚧">
<strong>Draft.</strong> Replace placeholders before PR.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
Node: 20.x (per project root)
TypeScript: 5.4.x (strict mode, noImplicitAny, etc.)
tree-sitter-cli: 0.25.4 (global or devDependency)
tree-sitter-python: 0.23.6 (npm)
tree-sitter-javascript: 0.23.1 (npm)
tree-sitter (Node bindings): latest compatible
ts-node-dev, jest: per M0 guidelines
```

---

## 🎯 Definition of Done

- Static parser-lib package exists and is published in the monorepo.
- CLI command `pnpm run build-graph` analyzes Python/JS files and emits a valid JSON-LD graph.
- --dry-run flag supported for validation without file writes.
- All code, tests, and docs merged under `m1-parser` branch and reviewed.

---

## 📦 Deliverables

| Path                        | Must contain …                                 |
|-----------------------------|-----------------------------------------------|
| packages/parser-lib/        | Parsing logic, AST utils, JSON-LD builders    |
| packages/parser-lib/tests/  | Jest unit tests, fixtures                     |
| backend/src/cli.ts          | CLI entrypoint for build-graph                |
| .github/workflows/ci.yml    | CI steps for install, test, dry-run           |
| docs/tech-specs/            | Spec and usage docs for build-graph           |
| graph.jsonld (output)       | JSON-LD graph of all detected functions       |

---

## 🗂 Directory / API Diagram

```text
packages/
├─ shared/
├─ parser-lib/
│   ├─ src/
│   │   ├─ parser.ts
│   │   ├─ astUtils.ts
│   │   ├─ jsonld.ts
│   │   └─ index.ts
│   ├─ tests/
│   ├─ package.json
│   └─ tsconfig.json
backend/
└─ src/
    └─ cli.ts
```

---

## 🧠 Key Decisions

| Topic             | Decision                                                                 | Rationale                |
|-------------------|--------------------------------------------------------------------------|--------------------------|
| Parser library    | Use Tree-sitter for Python/JS AST extraction                             | Fast, robust, incremental|
| Output format     | JSON-LD with minimal context, one node per function                      | Linked Data, extensible  |
| CLI integration   | CLI in backend/src/cli.ts, invoked via pnpm script                      | Consistent with M0       |
| Dry-run           | --dry-run flag logs actions, skips file writes                           | Safe validation          |
| Test coverage     | ≥80% on parser-lib, CLI integration tested                               | Quality, maintainability |

---

## ✅ Success Criteria

- CLI (`pnpm run build-graph`) outputs valid JSON-LD for all detected functions.
- Test suite passes (parser-lib unit, CLI integration) with satisfactory coverage.
- CI pipeline runs tests, coverage, and dry-run check on push.
- Code adheres to project rules (strict TS, linted, named exports).
- All deliverables merged under `m1-parser` and reviewed.

---

## 📋 Process Requirements (Agent Rules)

**MANDATORY**: Configure these rules in your agent before starting implementation. These rules ensure systematic milestone execution and quality compliance.

### Quick Configuration for M1

For complete agent configuration templates, see [Core Agent Rules](../process/agent-rules/core.mdx).

**Replace these placeholders in the templates:**
- `{MILESTONE_ID}` → `M1`
- `{MILESTONE_TITLE}` → `Static Graph Builder`
- `{milestone_id}` → `milestone-m1`
- `{milestone_script}` → `m1`

### M1-Specific Quality Rules
In addition to the core rules, M1 requires:
- **Tree-sitter integration** with proper grammar installation
- **JSON-LD validation** for output format compliance
- **CLI integration testing** with dry-run support
- **Parser library testing** with 80%+ coverage

### Process Validation Checklist

> **📋 Complete Process Checklist:** For comprehensive pre-implementation, during implementation, and post-implementation checklists, see [Core Process Guidelines](../process/agent-rules/core.mdx#📋-process-requirements-agent-rules).

**M1-Specific Validation Points:**
- [ ] Tree-sitter CLI and grammars properly installed
- [ ] JSON-LD output format validated against schema
- [ ] CLI dry-run functionality tested
- [ ] Parser library unit tests with fixtures

---

## 🔨 Task Breakdown

| #  | Branch name   | Checklist item                                                                 |
|----|--------------|--------------------------------------------------------------------------------|
| 1  | m1-parser    | Initialize parser-lib package, setup tsconfig, add dependencies                |
| 2  | m1-parser    | Implement Tree-sitter parsing for Python/JS in parser-lib                      |
| 3  | m1-parser    | AST traversal and function extraction logic                                    |
| 4  | m1-parser    | JSON-LD node construction and graph assembly                                   |
| 5  | m1-parser    | CLI tool: backend/src/cli.ts, argument parsing, dry-run support                |
| 6  | m1-parser    | File I/O: output JSON-LD, error handling                                       |
| 7  | m1-parser    | Jest unit tests for parser-lib, fixtures for .py/.js                           |
| 8  | m1-parser    | CLI integration tests (backend/tests/)                                         |
| 9  | m1-parser    | CI: update workflow to install grammars, run tests, dry-run check              |
| 10 | m1-parser    | Documentation: update README, tech-spec, output format                         |
| 11 | m1-parser    | Code review: lint, type safety, function size, TODOs for open questions        |

---

## 🤖 CI Pipeline (ready-to-copy)

```yaml
- name: Install tree-sitter CLI and grammars
  run: pnpm install --recursive

- name: Run tests
  run: pnpm test --recursive

- name: Build-graph dry-run check
  run: pnpm run build-graph -- --dry-run tests/fixtures/

- name: Lint/Format
  run: pnpm lint && pnpm format

# Optionally, add coverage gate
# - name: Check coverage
#   run: pnpm coverage
```

---

## 🧪 Acceptance Tests

- **Single-function file:** Given a Python file `a.py`:
  ```python
  def add(x, y): return x + y
  ```
  Running `pnpm run build-graph a.py` produces a JSON-LD node with `@type: Function`, `name: "add"`, parameters `x`, `y`, and `definedIn: "a.py"`.

- **JavaScript support:** Given a JS file `b.js`:
  ```js
  function greet(name) { console.log(name); }
  ```
  Output JSON-LD includes a Function node with `name: "greet"` and parameter `"name"`.

- **Directory recursion:** CLI on a directory with multiple `.py`/`.js` files combines all functions into one JSON-LD document.

- **Dry-run mode:** `--dry-run` returns exit code 0, prints summary, does not write output file.

- **CLI error handling:** Unsupported file types are skipped with a warning. Syntax errors are reported and handled gracefully.

- **CI check:** In CI, `pnpm run build-graph` (on a test dir) completes and produces valid JSON.

---

## 🔄 Document History

| Version | Date       | Changes                | Author           | Milestone Status |
|---------|------------|------------------------|------------------|------------------|
| 0.2.0   | 2024-05-25 | Initial conversion     | nitishMehrotra   | Draft            |

### Status Progression
- **Draft** → **Approved** → **In Progress** → **In Review** → **Completed**

### Update Guidelines
- Increment version for significant changes (requirements, scope)
- Update status as implementation progresses
- Document all major decisions and scope changes
- Link to ADRs for architectural decisions

---

## 📚 Related Documentation

### Cross-References
- **ADRs:** See docs/tech-specs/adrs/ for architectural decisions
- **Dependencies:** M0 milestone, shared types
- **Domains:** See docs/tech-specs/domains/

### External Resources
- [Tree-sitter](https://tree-sitter.github.io/tree-sitter/)
- [tree-sitter-cli (npm)](https://www.npmjs.com/package/tree-sitter-cli)
- [tree-sitter-python (npm)](https://www.npmjs.com/package/tree-sitter-python?activeTab=versions)
- [tree-sitter-javascript (npm)](https://www.npmjs.com/package/tree-sitter-javascript)
- [JSON-LD 1.1](https://www.w3.org/TR/json-ld11/)

<Callout emoji="📝">
Must pass <Link href="../spec-checklist.mdx">spec-checklist</Link> & dry-run before moving to <code>status: Approved</code>.
</Callout>

<Callout emoji="🔗">
Remember to update the <Link href="../milestone-log.mdx">milestone-log.mdx</Link> when this milestone's status or progress changes.
</Callout>