#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

class AgentConfigValidator {
    constructor(milestoneId, agentType) {
        this.milestoneId = milestoneId;
        this.agentType = agentType;
        this.configPath = `tech-specs/process/agent-rules/${agentType}.mdx`;
    }

    validate() {
        console.log(`🔍 Validating ${this.agentType} configuration for ${this.milestoneId}`);

        const results = {
            syntaxValid: this.validateSyntax(),
            contentComplete: this.validateContent(),
            placeholdersReplaced: this.validatePlaceholders(),
            integrationReady: this.validateIntegration()
        };

        const allValid = Object.values(results).every(result => result.valid);

        if (allValid) {
            console.log('✅ Agent configuration validation passed');
            return true;
        } else {
            console.log('❌ Agent configuration validation failed');
            this.reportErrors(results);
            return false;
        }
    }

    validateSyntax() {
        try {
            if (!fs.existsSync(this.configPath)) {
                return { valid: false, errors: [`Configuration file not found: ${this.configPath}`] };
            }

            const content = fs.readFileSync(this.configPath, 'utf8');
            
            // Check for basic markdown structure
            const errors = [];
            if (!content.includes('# ')) {
                errors.push('Missing main heading');
            }
            if (!content.includes('## ')) {
                errors.push('Missing section headings');
            }

            return { valid: errors.length === 0, errors };
        } catch (error) {
            return { valid: false, errors: [`Syntax validation error: ${error.message}`] };
        }
    }

    validateContent() {
        try {
            const content = fs.readFileSync(this.configPath, 'utf8');
            const errors = [];

            // Check for required sections
            const requiredSections = [
                'CORE RULES',
                'QUALITY REQUIREMENTS',
                'ERROR HANDLING'
            ];

            requiredSections.forEach(section => {
                if (!content.includes(section)) {
                    errors.push(`Missing required section: ${section}`);
                }
            });

            // Check for core process references
            const coreProcesses = [
                'milestone-implementation',
                'quality-assurance',
                'documentation',
                'git-workflow'
            ];

            let missingProcesses = 0;
            coreProcesses.forEach(process => {
                if (!content.includes(process)) {
                    missingProcesses++;
                }
            });

            if (missingProcesses > 2) {
                errors.push(`Missing references to core processes (${missingProcesses}/${coreProcesses.length})`);
            }

            return { valid: errors.length === 0, errors };
        } catch (error) {
            return { valid: false, errors: [`Content validation error: ${error.message}`] };
        }
    }

    validatePlaceholders() {
        try {
            const content = fs.readFileSync(this.configPath, 'utf8');
            const errors = [];

            // Check for unreplaced placeholders
            const placeholders = [
                '{MILESTONE_ID}',
                '{MILESTONE_TITLE}',
                '{milestone_id}',
                '{milestone_script}'
            ];

            placeholders.forEach(placeholder => {
                if (content.includes(placeholder)) {
                    errors.push(`Unreplaced placeholder found: ${placeholder}`);
                }
            });

            return { valid: errors.length === 0, errors };
        } catch (error) {
            return { valid: false, errors: [`Placeholder validation error: ${error.message}`] };
        }
    }

    validateIntegration() {
        try {
            const content = fs.readFileSync(this.configPath, 'utf8');
            const errors = [];

            // Check for integration points
            if (!content.includes('spec-lint.mjs')) {
                errors.push('Missing spec-lint.mjs validation reference');
            }

            if (!content.includes('acceptance.sh')) {
                errors.push('Missing acceptance script reference');
            }

            if (!content.includes('15min') && !content.includes('15 min')) {
                errors.push('Missing real-time documentation requirement');
            }

            return { valid: errors.length === 0, errors };
        } catch (error) {
            return { valid: false, errors: [`Integration validation error: ${error.message}`] };
        }
    }

    reportErrors(results) {
        console.log('\n📋 Validation Details:');
        Object.entries(results).forEach(([check, result]) => {
            const status = result.valid ? '✅' : '❌';
            console.log(`${status} ${check}`);
            if (!result.valid && result.errors.length > 0) {
                result.errors.forEach(error => {
                    console.log(`   - ${error}`);
                });
            }
        });
    }
}

// CLI usage
const [milestoneId, agentType] = process.argv.slice(2);
if (!milestoneId || !agentType) {
    console.log('Usage: node validate-agent-config.mjs <milestone-id> <agent-type>');
    console.log('Example: node validate-agent-config.mjs M1 claude');
    console.log('\nAvailable agent types: claude, copilot, cursor, augment, custom');
    process.exit(1);
}

const validator = new AgentConfigValidator(milestoneId, agentType);
const isValid = validator.validate();
process.exit(isValid ? 0 : 1);
