#!/usr/bin/env bash
set -euo pipefail

# Generate agent configuration from template
# Usage: node scripts/generate-agent-config.sh <milestone-id> <agent-type> <milestone-title>

MILESTONE_ID=${1:-""}
AGENT_TYPE=${2:-""}
MILESTONE_TITLE=${3:-""}

if [[ -z "$MILESTONE_ID" || -z "$AGENT_TYPE" || -z "$MILESTONE_TITLE" ]]; then
    echo "Usage: $0 <milestone-id> <agent-type> <milestone-title>"
    echo "Example: $0 M1 claude 'Static Graph Builder'"
    echo ""
    echo "Available agent types:"
    echo "  - claude"
    echo "  - copilot"
    echo "  - cursor"
    echo "  - augment"
    echo "  - custom"
    exit 1
fi

# Validate agent type
TEMPLATE="tech-specs/process/agent-rules/${AGENT_TYPE}.mdx"
if [[ ! -f "$TEMPLATE" ]]; then
    echo "❌ Agent type '$AGENT_TYPE' not found. Template does not exist: $TEMPLATE"
    echo "Available agent types:"
    ls tech-specs/process/agent-rules/*.mdx | grep -v validation | sed 's/.*\///;s/\.mdx$//' | sed 's/^/  - /'
    exit 1
fi

# Create output directory
MILESTONE_LOWER=$(echo "$MILESTONE_ID" | tr '[:upper:]' '[:lower:]')
OUTPUT="../work-log/milestone-${MILESTONE_LOWER}/agent-config-${AGENT_TYPE}.md"
mkdir -p "$(dirname "$OUTPUT")"

echo "🔧 Generating agent configuration..."
echo "   Milestone: $MILESTONE_ID"
echo "   Agent: $AGENT_TYPE"
echo "   Title: $MILESTONE_TITLE"
echo "   Template: $TEMPLATE"
echo "   Output: $OUTPUT"

# Copy template and replace placeholders
sed -e "s/{MILESTONE_ID}/$MILESTONE_ID/g" \
    -e "s/{MILESTONE_TITLE}/$MILESTONE_TITLE/g" \
    -e "s/{milestone_id}/${MILESTONE_LOWER}/g" \
    -e "s/{milestone_script}/${MILESTONE_LOWER}/g" \
    "$TEMPLATE" > "$OUTPUT"

echo "✅ Generated agent configuration: $OUTPUT"
echo ""
echo "📝 Next steps:"
echo "   1. Review the generated configuration"
echo "   2. Customize any agent-specific settings"
echo "   3. Validate with: node scripts/validate-agent-config.mjs $MILESTONE_ID $AGENT_TYPE"
echo "   4. Use the configuration in your agent setup"
